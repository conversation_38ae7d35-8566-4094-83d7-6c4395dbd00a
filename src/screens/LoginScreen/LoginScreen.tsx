import React, { useContext, useEffect } from 'react';
import LoginScreenTablet from './LoginScreen.tablet';
import LoginScreenPhone from './LoginScreen.phone';
import useLayoutAdoptionCheck from 'hooks/useDeviceCheck';
import { ToolbarContext } from 'features/reportGeneration/ph/ToolbarProvider';
import { country } from 'utils/context';
import SummaryV1Test from 'features/ecoach/screens/summary/SummaryV1Test';

export default function LoginScreen() {
  const { isTabletMode } = useLayoutAdoptionCheck();

  // Reset PH report generation context values when the user navigates back to the login screen
  const { resetContextValues } = useContext(ToolbarContext);
  useEffect(() => {
    if (country === 'ph') resetContextValues();
  }, []);

  // return isTabletMode ? <LoginScreenTablet /> : <LoginScreenPhone />;
  return isTabletMode ? <LoginScreenTablet /> : <SummaryV1Test />;
}
