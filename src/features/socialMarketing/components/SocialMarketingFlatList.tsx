import styled from '@emotion/native';
import { useTheme } from '@emotion/react';
import {
  ListRenderItem,
  MasonryFlashList,
  MasonryFlashListProps,
  MasonryListRenderItemInfo,
} from '@shopify/flash-list';
import { SocialMarketingStoryTemplate } from '../types';
import { Typography } from 'cube-ui-components';
import React, { useCallback } from 'react';
import { View } from 'react-native';
import { MAX_NUMBER_OF_TEMPLATE_SHOWN } from 'features/socialMarketing/utils/const';
import { useGetTabBarHeight } from '../hooks/useLayoutStore';
import GalleryMediaCard from './GalleryMediaCard';

const ListHeaderContainer = styled.View(({ theme: { space } }) => ({
  paddingVertical: space[2],
  paddingHorizontal: space[1],
}));
const MediaSeparator = styled.View(({ theme: { space } }) => ({
  width: space[2],
  height: space[2],
}));

type SocialMarketingFlatListProps = Omit<
  MasonryFlashListProps<SocialMarketingStoryTemplate>,
  'renderItem' | 'onEndReached' | 'ListHeaderComponent'
> & {
  /**
   * ExtraListHeaderComponent can be used to add custom components or elements
   * at the top of the list, such as filters, search bars, or other UI elements.
   */
  ExtraListHeaderComponent?: React.ReactNode;
  /**
   * Title text to be displayed at the top of the list.
   * This can be used to provide context or a heading for the list.
   */
  title?: string;
  /**
   * Description text to be displayed below the title.
   * This can be used to provide additional context or instructions.
   */
  description?: string;
  /**
   * Custom render function for each item in the list.
   * If not provided, a default GalleryMediaCard will be used.
   */
  renderItem?: ListRenderItem<SocialMarketingStoryTemplate>;
  /**
   * Callback for when an item is pressed.
   * If you provide a custom renderItem, you can handle the press event within that function.
   * @param item The SocialMarketingStoryTemplate item that was pressed.
   */
  onItemPress?: (item: SocialMarketingStoryTemplate) => void;
  /**
   * Callback for when the end of the list is reached, used for pagination.
   */
  onLoadMore?: () => void;
};

export default function SocialMarketingFlatList({
  ExtraListHeaderComponent,
  title,
  description,
  onItemPress,
  ...props
}: SocialMarketingFlatListProps) {
  const tabBarHeight = useGetTabBarHeight();
  const { colors, space } = useTheme();

  const handleRenderMediaItem = useCallback(
    ({
      item,
      index,
    }: MasonryListRenderItemInfo<SocialMarketingStoryTemplate>) => (
      <GalleryMediaCard
        index={index}
        story={item}
        onPress={() => onItemPress?.(item)}
      />
    ),
    [onItemPress],
  );

  return (
    <MasonryFlashList
      numColumns={2}
      keyExtractor={item => item.storyId}
      renderItem={handleRenderMediaItem}
      estimatedItemSize={MAX_NUMBER_OF_TEMPLATE_SHOWN}
      {...props}
      style={{ flex: 1 }}
      contentContainerStyle={{
        backgroundColor: colors.background,
        paddingVertical: space[1],
        paddingHorizontal: space[3],
        paddingBottom: tabBarHeight + space[2], // Adjust for bottom tab bar height
      }}
      onEndReached={props.onLoadMore}
      ItemSeparatorComponent={() => <MediaSeparator />}
      ListHeaderComponent={
        <View>
          <ListHeaderContainer>
            {title && <Typography.H5 fontWeight="bold">{title}</Typography.H5>}
            {description && <Typography.Label>{description}</Typography.Label>}
          </ListHeaderContainer>
          {React.isValidElement(ExtraListHeaderComponent)
            ? ExtraListHeaderComponent
            : null}
        </View>
      }
    />
  );
}
