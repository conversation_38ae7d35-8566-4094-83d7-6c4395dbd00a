import React from 'react';
import styled from '@emotion/native';
import { View } from 'react-native';
import { H7 } from 'cube-ui-components';
import { colors, sizes } from 'cube-ui-components/dist/cjs/theme/base';
import { SkillSetDetailV2 } from 'features/ecoach/api/conversationApi';

const ChartContainer = styled(View)(() => ({
  backgroundColor: colors.white,
  marginHorizontal: sizes[4],
  marginTop: sizes[4],
  padding: sizes[4],
  borderRadius: 12,
}));

const SkillChartRow = styled(View)(() => ({
  flexDirection: 'row',
  justifyContent: 'center',
  alignItems: 'flex-end',
  gap: 4,
}));

const SkillBarContainer = styled(View)(() => ({
  marginBottom: sizes[4],
  width: 83,
}));

const SkillName = styled(H7)(() => ({
  color: colors.fwdDarkGreen[100],
  fontWeight: '500',
  marginBottom: sizes[2],
  textAlign: 'center',
  fontSize: 12,
  lineHeight: 15,
}));

const SkillBarBackground = styled(View)(() => ({
  height: 83,
  backgroundColor: colors.fwdDarkGreen[10],
  borderRadius: 8,
  borderWidth: 1,
  borderColor: colors.white,
  overflow: 'hidden',
  position: 'relative',
}));

const SkillBarFill = styled(View)<{ height: number; backgroundColor: string }>(
  ({ height, backgroundColor }) => ({
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    height: `${height}%`,
    backgroundColor,
    borderRadius: height === 100 ? 8 : '0px 0px 8px 8px',
  })
);

const SkillScore = styled(H7)(() => ({
  color: colors.fwdDarkGreen[100],
  fontWeight: 'bold',
  textAlign: 'center',
  marginTop: sizes[2],
}));

interface SkillChartProps {
  skills: SkillSetDetailV2[];
}

const SkillChart: React.FC<SkillChartProps> = ({ skills }) => {
  const getSkillBarColor = (score: string) => {
    const numScore = parseInt(score);
    if (numScore >= 80) return colors.fwdLightGreen[100]; // Green for excellent
    if (numScore >= 60) return colors.fwdOrange[100]; // Orange for good
    if (numScore >= 30) return colors.fwdRed[100]; // Red for needs improvement
    return colors.fwdDarkGreen[20]; // Gray for poor
  };

  const formatSkillName = (name: string) => {
    // Split long skill names into multiple lines for better display
    return name.replace(/\s+/g, '\n');
  };

  return (
    <ChartContainer>
      <SkillChartRow>
        {skills.slice(0, 4).map((skill, index) => (
          <SkillBarContainer key={index}>
            <SkillBarBackground>
              <SkillBarFill
                height={parseInt(skill.score)}
                backgroundColor={getSkillBarColor(skill.score)}
              />
            </SkillBarBackground>
            <SkillScore>{skill.score}</SkillScore>
            <SkillName>{formatSkillName(skill.name)}</SkillName>
          </SkillBarContainer>
        ))}
      </SkillChartRow>
    </ChartContainer>
  );
};

export default SkillChart;
