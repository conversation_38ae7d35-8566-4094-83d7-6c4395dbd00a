import React from 'react';
import styled from '@emotion/native';
import { View, TouchableOpacity } from 'react-native';
import { H6, H7, Body, SmallLabel } from 'cube-ui-components';
import { colors, sizes } from 'cube-ui-components/dist/cjs/theme/base';
import { FeedbackCard } from 'features/ecoach/api/conversationApi';

const CardContainer = styled(View)(() => ({
  backgroundColor: colors.white,
  borderRadius: 12,
  borderWidth: 1,
  borderColor: colors.fwdDarkGreen[10],
  padding: sizes[4],
  marginBottom: sizes[3],
}));

const CardHeader = styled(View)(() => ({
  flexDirection: 'row',
  alignItems: 'center',
  marginBottom: sizes[3],
}));

const NumberBadge = styled(View)(() => ({
  width: 24,
  height: 24,
  backgroundColor: colors.fwdOrange[100],
  borderRadius: 12,
  alignItems: 'center',
  justifyContent: 'center',
  marginRight: sizes[2],
}));

const NumberText = styled(SmallLabel)(() => ({
  color: colors.white,
  fontWeight: 'bold',
}));

const CardTitle = styled(H6)(() => ({
  color: colors.fwdDarkGreen[100],
  fontWeight: 'bold',
  flex: 1,
}));

const Divider = styled(View)(() => ({
  height: 1,
  backgroundColor: colors.fwdDarkGreen[10],
  marginVertical: sizes[3],
}));

const ObservationText = styled(Body)(() => ({
  color: colors.fwdDarkGreen[70],
  marginBottom: sizes[3],
  fontSize: 10,
  lineHeight: 15,
}));

const InsightSection = styled(View)(() => ({
  backgroundColor: colors.fwdOrange[10],
  borderRadius: 16,
  padding: sizes[4],
}));

const InsightLabel = styled(H7)(() => ({
  color: colors.fwdDarkGreen[100],
  fontWeight: 'bold',
  marginBottom: sizes[2],
}));

const InsightText = styled(Body)(() => ({
  color: colors.fwdDarkGreen[100],
  fontSize: 12,
  lineHeight: 18,
}));

const ExampleSection = styled(View)(() => ({
  marginTop: sizes[4],
}));

const ExampleLabel = styled(H7)(() => ({
  color: colors.fwdDarkGreen[100],
  fontWeight: 'bold',
  marginBottom: sizes[1],
}));

const ExampleText = styled(Body)(() => ({
  color: colors.fwdDarkGreen[100],
  fontSize: 12,
  lineHeight: 18,
}));

interface ImprovementCardProps {
  card: FeedbackCard;
  index: number;
}

const ImprovementCard: React.FC<ImprovementCardProps> = ({ card, index }) => {
  return (
    <CardContainer>
      <CardHeader>
        <NumberBadge>
          <NumberText>{index + 1}</NumberText>
        </NumberBadge>
        <CardTitle>{card.title}</CardTitle>
      </CardHeader>

      <Divider />

      <ObservationText>{card.observation}</ObservationText>

      <InsightSection>
        <InsightLabel>Insights</InsightLabel>
        <InsightText>{card.insight}</InsightText>
        
        <ExampleSection>
          <ExampleLabel>Example</ExampleLabel>
          <ExampleText>{card.action}</ExampleText>
        </ExampleSection>
      </InsightSection>
    </CardContainer>
  );
};

export default ImprovementCard;
