import React, { useState } from 'react';
import { RouteProp, useNavigation, useRoute } from '@react-navigation/native';
import { RootStackParamList } from 'types';
import { useTranslation } from 'react-i18next';
import LoadingPage from 'features/ecoach/screens/loading/Loading';
import styled from '@emotion/native';
import {
  Button,
  H3,
  H5,
  H8,
  Icon,
  Label,
  SmallLabel,
} from 'cube-ui-components';
import { colors, sizes } from 'cube-ui-components/dist/cjs/theme/base';
import { reportBackground } from 'features/ecoach/assets';
import {
  Image,
  ImageBackground, Platform,
  ScrollView,
  TouchableOpacity,
  View,
} from 'react-native';
import { format, parseISO } from 'date-fns';
import { EcoachParamList } from 'types/navigation';
import {
  emptyStar,
  halfStar,
  oneHalfStar,
  oneStar,
  threeStar,
  twoHalfStar,
  twoStar,
} from '../../assets';
import ScoreContainer from '../ScoreContainer';
import Spacer from 'features/lead/my/LeadProfile/components/Spacer';
import { useTheme } from '@emotion/react/dist/emotion-react.cjs';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useQuery, useQueryClient } from '@tanstack/react-query';
import {
  ConversationData,
  ConversationDataUnion,
  ConversationDataV2,
  ConversationType,
  getConversationData,
  isConversationDataV2,
} from 'features/ecoach/api/conversationApi';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { ReportQualityFeedback } from 'features/ecoach/components/feedback/ReportQualityFeedback';
import FeedbackModal from 'features/ecoach/components/modals/FeedbackModal';
import ReportReady from 'features/ecoach/screens/ReportReady';
import { extractScore } from 'features/ecoach/utils/extractScore';
import { SESSION_HISTORY_CONVERSATION_HISTORY_V_2_CACHE_KEY } from 'features/ecoach/hooks/useReportHistory';
import { SESSION_HISTORY_CONVERSATION_HISTORY_LATEST_CACHE_KEY } from 'features/ecoach/hooks/useReportHistoryLatest';
import { StatusBar } from 'expo-status-bar';
import SummaryV1 from './SummaryV1';

const HorizontalView = styled(View)(() => ({
  flexDirection: 'row',
  alignItems: 'center',
  justifyContent: 'space-between',
  gap: sizes[1],
}));

const BtnGroupView = styled(View)(() => ({
  flexDirection: 'row',
  alignItems: 'center',
  justifyContent: 'space-between',
  padding: sizes[1],
  gap: sizes[4],
}));

const LeftView = styled(View)(() => ({
  alignItems: 'flex-start',
  gap: sizes[1],
}));

const RightView = styled(View)(() => ({
  alignItems: 'flex-start',
  gap: sizes[1],
  flexGrow: 1,
  maxWidth: '50%',
}));

const PageImageBackground = styled(ImageBackground)(() => ({
  flex: 1,
  width: '100%',
  height: '100%',
}));

const YourReportText = styled(H5)(() => ({
  color: colors.fwdDarkGreen[100],
  paddingBottom: sizes[6],
}));

const DifficultyTypeTitle = styled(H8)(() => ({
  color: colors.fwdDarkGreen[100],
}));

const StarImageContainer = styled(View)(() => ({
  alignItems: 'center',
  flexDirection: 'row',
}));

const GreyDot = styled(View)(() => ({
  width: 10,
  height: 10,
  backgroundColor: colors.fwdDarkGreen[20],
  borderRadius: 5,
}));

const StarImage = styled(Image)(() => ({
  flex: 1,
  height: 30,
  marginTop: sizes[4],
}));

const ScoreText = styled(Label)(() => ({
  textAlign: 'center',
  marginTop: sizes[2],
}));

const HighlightedScoreText = styled(H3)(() => ({
  textAlign: 'center',
}));

const BackBtn = styled(TouchableOpacity)(() => ({
  paddingTop: Platform.OS === 'ios' ? 0 : sizes[5],
  paddingHorizontal: sizes[4],
}));

const LoadingContainer = styled(View)(() => ({
  flex: 1,
}));

const SummaryPage = () => {
  const { t } = useTranslation('ecoach');
  const queryClient = useQueryClient();

  const navigation =
    useNavigation<NativeStackNavigationProp<RootStackParamList>>();
  const { elevation } = useTheme();

  const {
    params: { conversationId, session, reTake },
  } = useRoute<RouteProp<EcoachParamList, 'Summary'>>();

  const [summaryInfo, setSummaryInfo] = useState<ConversationDataUnion | undefined>(
    session,
  );

  const [showReportReady, setShowReportReady] = useState<boolean>(false);
  const [showFeedbackModal, setShowFeedbackModal] = useState<boolean>(!session);

  queryClient.removeQueries([
    `${SESSION_HISTORY_CONVERSATION_HISTORY_LATEST_CACHE_KEY}-${ConversationType.CUSTOMER}`,
    `${SESSION_HISTORY_CONVERSATION_HISTORY_V_2_CACHE_KEY}-${ConversationType.CUSTOMER}`,
    `${SESSION_HISTORY_CONVERSATION_HISTORY_LATEST_CACHE_KEY}-${ConversationType.QUICKFIRE}`,
    `${SESSION_HISTORY_CONVERSATION_HISTORY_V_2_CACHE_KEY}-${ConversationType.QUICKFIRE}`,
  ]);

  useQuery({
    queryKey: ['conversationId', conversationId],
    queryFn: () => getConversationData(conversationId),
    refetchInterval: summaryInfo ? false : 5000,
    enabled: !session,
    onSuccess: data => {
      if (data.report_is_ready === 'true' && !session) {
        setSummaryInfo(data);
        setShowReportReady(true);
      }
    },
    onError: error => {
      console.log(
        'SummaryPage getConversationData error',
        JSON.stringify(error),
      );
    },
  });

  const goHistoryScreen = () => {
    navigation.push('SessionHistory', {});
  };

  const tryAgain = () => {
    navigation.goBack();
    reTake && reTake();
  };

  const getStarImage = (overallScore: number) => {
    if (overallScore < 14) return emptyStar;
    else if (overallScore >= 14 && overallScore < 28) return halfStar;
    else if (overallScore >= 28 && overallScore < 42) return oneStar;
    else if (overallScore >= 42 && overallScore < 56) return oneHalfStar;
    else if (overallScore >= 56 && overallScore < 70) return twoStar;
    else if (overallScore >= 70 && overallScore < 84) return twoHalfStar;
    else if (overallScore >= 84) return threeStar;
    return emptyStar;
  };

  const convertSecondsToMins = (duration: number) => {
    const minutes = Math.floor(duration / 60);
    const seconds = (duration % 60).toFixed(2); // To get two decimal places for seconds
    return `${minutes} ${t('min')} ${seconds} ${t('sec')}`;
  };

  const handleUserUpdate = () => {
    !showFeedbackModal && setShowFeedbackModal(true);
  };

  if (!summaryInfo || showFeedbackModal || showReportReady)
    return (
      <LoadingContainer>
        {!summaryInfo ? (
          <LoadingPage />
        ) : showReportReady ? (
          <ReportReady hideReadyReport={() => setShowReportReady(false)} />
        ) : null}
        <FeedbackModal
          conversationId={conversationId}
          visible={showFeedbackModal}
          setVisible={setShowFeedbackModal}
          handleUserUpdate={handleUserUpdate}
        />
      </LoadingContainer>
    );

  // Check if this is the new V2 format and render SummaryV1
  if (isConversationDataV2(summaryInfo)) {
    return (
      <SummaryV1
        conversationData={summaryInfo}
        session={session}
        reTake={reTake}
      />
    );
  }

  // Cast to legacy ConversationData since we've already checked it's not V2
  const legacySummaryInfo = summaryInfo as ConversationData;
  const { conversation_id, datetime, difficulty, duration, report } =
    legacySummaryInfo || {};

  const {
    overall_score,
    customer_relationship_score,
    skill_set_details,
    customer_discovery_score,
    applied_product_knowledge_score,
    objection_handling_closing_score,
    communication_skills_score,
  } = report || {};

  const goBackBtn = () => {
    if (session) {
      navigation.goBack();
    } else {
      navigation.navigate('EcoachHome');
    }
  };

  return (
    <View style={{ flex: 1 }}>
      <StatusBar hidden />
      <PageImageBackground source={reportBackground} resizeMode="cover">
        <SafeAreaView>
          <BackBtn onPress={goBackBtn}>
            {session ? (
              <Icon.ArrowLeft fill={colors.fwdDarkGreen[100]} />
            ) : (
              <Icon.Close fill={colors.fwdDarkGreen[100]} />
            )}
          </BackBtn>
          <ScrollView
            style={{ margin: sizes[4] }}
            showsVerticalScrollIndicator={false}>
            <HorizontalView>
              <YourReportText fontWeight={'bold'}>
                {t('yourReport')}
              </YourReportText>
            </HorizontalView>
            <HorizontalView>
              <LeftView>
                <HorizontalView>
                  <SmallLabel fontWeight="bold" color={colors.fwdDarkGreen[50]}>
                    {t('ssID')}
                  </SmallLabel>
                  <SmallLabel
                    fontWeight="normal"
                    color={colors.fwdDarkGreen[50]}>
                    {conversation_id}
                  </SmallLabel>
                </HorizontalView>

                <HorizontalView>
                  <DifficultyTypeTitle fontWeight={'normal'}>
                    {t('difficulty')}
                  </DifficultyTypeTitle>
                  <H8 fontWeight="bold" color={colors.fwdDarkGreen[100]}>
                    {difficulty === '1' ? t('beginner') : t('expert')}
                  </H8>
                </HorizontalView>
              </LeftView>
              <RightView>
                <HorizontalView>
                  <SmallLabel fontWeight="bold" color={colors.fwdDarkGreen[50]}>
                    {t('dateAndTime')}
                  </SmallLabel>
                  <SmallLabel
                    fontWeight="normal"
                    color={colors.fwdDarkGreen[50]}>
                    {format(parseISO(datetime), 'dd MMM yyyy')}
                  </SmallLabel>
                </HorizontalView>

                <HorizontalView>
                  <H8 fontWeight="normal" color={colors.fwdDarkGreen[100]}>
                    {t('duration')}
                  </H8>
                  <H8 fontWeight="bold" color={colors.fwdDarkGreen[100]}>
                    {convertSecondsToMins(duration)}
                  </H8>
                </HorizontalView>
              </RightView>
            </HorizontalView>

            {overall_score && skill_set_details && (
              <View
                style={{
                  backgroundColor: colors.white,
                  borderRadius: 16,
                  borderWidth: 4,
                  borderColor: colors.fwdLightGreen[100],
                  shadowColor: colors.black,
                  paddingHorizontal: sizes[4],
                  paddingBottom: sizes[4],
                  marginTop: sizes[6],
                  ...elevation[16],
                }}>
                <StarImageContainer>
                  <GreyDot />
                  <StarImage
                    source={getStarImage(extractScore(overall_score))}
                    resizeMode="contain"
                  />
                  <GreyDot />
                </StarImageContainer>
                <ScoreText fontWeight={'bold'} color={colors.fwdDarkGreen[50]}>
                  {t('overallScore')}
                </ScoreText>
                <Label
                  fontWeight={'bold'}
                  color={colors.fwdDarkGreen[50]}
                  style={{ textAlign: 'center' }}>
                  <HighlightedScoreText
                    fontWeight={'bold'}
                    color={colors.fwdDarkGreen[100]}>
                    {extractScore(overall_score)}{' '}
                  </HighlightedScoreText>
                  / 100
                </Label>
                <Spacer height={sizes[4]} />
                {customer_relationship_score && (
                  <ScoreContainer
                    title={t('relationshipBuilding')}
                    score={extractScore(customer_relationship_score)}
                    skillSet={skill_set_details?.customer_relationship}
                    color={colors.fwdLightGreen[100]}
                    noBorderBottom={
                      !!customer_discovery_score ||
                      !!applied_product_knowledge_score ||
                      !!objection_handling_closing_score ||
                      !!communication_skills_score
                    }
                  />
                )}
                {customer_discovery_score && (
                  <ScoreContainer
                    title={t('customerDiscovery')}
                    score={extractScore(customer_discovery_score)}
                    skillSet={skill_set_details?.customer_discovery}
                    color={colors.fwdBlue[20]}
                    noBorderTop={!!customer_relationship_score}
                    noBorderBottom={
                      !!applied_product_knowledge_score ||
                      !!objection_handling_closing_score ||
                      !!communication_skills_score
                    }
                  />
                )}
                {applied_product_knowledge_score && (
                  <ScoreContainer
                    title={t('productKnowledge')}
                    score={extractScore(applied_product_knowledge_score)}
                    skillSet={skill_set_details?.applied_product_knowledge}
                    color={colors.fwdLightGreen[50]}
                    noBorderTop={
                      !!customer_discovery_score ||
                      !!customer_relationship_score
                    }
                    noBorderBottom={
                      !!objection_handling_closing_score ||
                      !!communication_skills_score
                    }
                  />
                )}
                {objection_handling_closing_score && (
                  <ScoreContainer
                    title={t('objectionsAndClosing')}
                    score={extractScore(objection_handling_closing_score)}
                    skillSet={skill_set_details?.objection_handling_closing}
                    color={colors.fwdBlue[100]}
                    noBorderTop={
                      !!customer_discovery_score ||
                      !!applied_product_knowledge_score ||
                      !!customer_relationship_score
                    }
                    noBorderBottom={!!communication_skills_score}
                  />
                )}
                {communication_skills_score && (
                  <ScoreContainer
                    title={t('speechAndAnalysis')}
                    score={extractScore(communication_skills_score)}
                    skillSet={skill_set_details?.communication_skills}
                    color={colors.fwdBlue[50]}
                    noBorderTop={
                      !!customer_discovery_score ||
                      !!applied_product_knowledge_score ||
                      !!objection_handling_closing_score ||
                      !!customer_relationship_score
                    }
                  />
                )}
                {!session ? (
                  <ReportQualityFeedback conversationId={conversationId} />
                ) : null}
                <Spacer height={sizes[3]} />
                <HorizontalView>
                  <GreyDot />
                  <GreyDot />
                </HorizontalView>
              </View>
            )}
            <Spacer height={sizes[4]} />
            {!session && (
              <BtnGroupView>
                <Button
                  variant={'primary'}
                  text={t('letTryAgain')}
                  onPress={tryAgain}
                  style={{ flex: 1 }}
                />
                <Button
                  variant={'secondary'}
                  text={t('viewHistory')}
                  onPress={goHistoryScreen}
                  style={{ flex: 1 }}
                />
              </BtnGroupView>
            )}
          </ScrollView>
        </SafeAreaView>
      </PageImageBackground>
    </View>
  );
};

export default SummaryPage;
