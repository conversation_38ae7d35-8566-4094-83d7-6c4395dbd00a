import { ConversationDataV2 } from 'features/ecoach/api/conversationApi';

export const mockConversationDataV2: ConversationDataV2 = {
  "report_is_ready": "true",
  "conversation_id": "OcDVT3Z7qL-RP6oSDZF-_",
  "datetime": "2025-08-06T06:33:07.839341+00:00",
  "difficulty": "3",
  "duration": 4.952734,
  "report": {
    "overall_score": "65",
    "last_score": "45",
    "overall_summary": "You did a good job of addressing the customer's concerns and closing the sale. Your communication was clear and you showed good product knowledge. However, there are areas where you can improve your objection handling and customer discovery techniques.",
    "skill_set_details": [
      {
        "name": "Applied Product Knowledge",
        "score": "60",
        "improvement": {
          "summary": "You demonstrated basic product knowledge but could provide more specific examples and benefits tailored to the customer's needs.",
          "cards": [
            {
              "cardType": "FEEDBACK",
              "title": "Missed Opportunity to Close",
              "observation": "You outlined value but didn't follow up with a clear next step or ask.",
              "insight": "Without a defined close, momentum is lost.",
              "action": "End strong with a specific CTA, like scheduling or commitment confirmation."
            },
            {
              "cardType": "FEEDBACK",
              "title": "Delayed Value Hook",
              "observation": "You said 'something cool from work' but didn't explain the benefit.",
              "insight": "Leads disengage quickly if the hook isn't immediate.",
              "action": "Offer a fast, tangible benefit early to spark curiosity."
            }
          ]
        },
        "strengths": {
          "summary": "You showed good understanding of the product features and were able to explain them clearly.",
          "cards": [
            {
              "cardType": "PRAISE",
              "title": "Clear Product Explanation",
              "observation": "You explained the product features in simple, understandable terms.",
              "insight": "Clear communication builds trust and understanding.",
              "action": "Continue to use simple language when explaining complex products."
            }
          ]
        }
      },
      {
        "name": "Customer Relationship",
        "score": "80",
        "improvement": {
          "summary": "Your relationship building skills are strong, but you could ask more discovery questions to better understand customer needs.",
          "cards": [
            {
              "cardType": "FEEDBACK",
              "title": "Limited Discovery Questions",
              "observation": "You focused on presenting rather than asking about customer needs.",
              "insight": "Understanding customer needs leads to better solutions.",
              "action": "Ask more open-ended questions about their current situation and goals."
            }
          ]
        },
        "strengths": {
          "summary": "You built excellent rapport with the customer and showed genuine interest in helping them.",
          "cards": [
            {
              "cardType": "PRAISE",
              "title": "Acknowledged Time Constraints",
              "observation": "You reassured the customer about meeting length and offered flexible scheduling.",
              "insight": "Empathy builds trust and keeps communication smooth.",
              "action": "Continue to align your message with the customer's time sensitivity."
            },
            {
              "cardType": "PRAISE",
              "title": "Clear Meeting Structure",
              "observation": "You described how the meeting would flow—overview, Q&A, and timing.",
              "insight": "Setting expectations up front reduces friction and shows professionalism.",
              "action": "Always outline structure when proposing meetings."
            }
          ]
        }
      },
      {
        "name": "Objection handling",
        "score": "30",
        "improvement": {
          "summary": "This is an area that needs significant improvement. You struggled to address customer concerns effectively.",
          "cards": [
            {
              "cardType": "FEEDBACK",
              "title": "Weak Objection Response",
              "observation": "When the customer raised price concerns, you didn't provide compelling counter-arguments.",
              "insight": "Unaddressed objections become barriers to closing.",
              "action": "Prepare specific responses to common objections and practice them regularly."
            },
            {
              "cardType": "FEEDBACK",
              "title": "Missed Opportunity to Reframe",
              "observation": "You accepted the customer's objection without trying to reframe their perspective.",
              "insight": "Reframing helps customers see value from a different angle.",
              "action": "Use techniques like 'I understand, and what if...' to introduce new perspectives."
            }
          ]
        },
        "strengths": {
          "summary": "You listened carefully to the customer's concerns and remained professional.",
          "cards": [
            {
              "cardType": "PRAISE",
              "title": "Active Listening",
              "observation": "You let the customer fully express their concerns before responding.",
              "insight": "Active listening makes customers feel heard and valued.",
              "action": "Continue to give customers space to express their thoughts completely."
            }
          ]
        }
      },
      {
        "name": "Communication Skills",
        "score": "75",
        "improvement": {
          "summary": "Your communication is generally clear, but you could be more concise and impactful.",
          "cards": [
            {
              "cardType": "FEEDBACK",
              "title": "Too Much Information",
              "observation": "You provided extensive details that may have overwhelmed the customer.",
              "insight": "Information overload can confuse rather than convince.",
              "action": "Focus on 2-3 key benefits that matter most to this specific customer."
            }
          ]
        },
        "strengths": {
          "summary": "You speak clearly and maintain a professional tone throughout the conversation.",
          "cards": [
            {
              "cardType": "PRAISE",
              "title": "Professional Tone",
              "observation": "You maintained a confident and professional demeanor throughout.",
              "insight": "Professional communication builds credibility and trust.",
              "action": "Continue to project confidence while remaining approachable."
            }
          ]
        }
      },
      {
        "name": "Customer Discovery",
        "score": "55",
        "improvement": {
          "summary": "You need to ask more probing questions to better understand customer needs and motivations.",
          "cards": [
            {
              "cardType": "FEEDBACK",
              "title": "Surface-Level Questions",
              "observation": "Your questions focused on basic information rather than deeper needs.",
              "insight": "Deep discovery reveals the real reasons customers buy.",
              "action": "Ask 'why' and 'what if' questions to uncover underlying motivations."
            }
          ]
        },
        "strengths": {
          "summary": "You showed genuine interest in understanding the customer's situation.",
          "cards": [
            {
              "cardType": "PRAISE",
              "title": "Genuine Interest",
              "observation": "You asked follow-up questions and showed you were listening.",
              "insight": "Genuine interest creates connection and trust.",
              "action": "Continue to show authentic curiosity about customer needs."
            }
          ]
        }
      }
    ]
  }
};
