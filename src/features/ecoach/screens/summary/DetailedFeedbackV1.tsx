import React, { useState } from 'react';
import { RouteProp, useNavigation, useRoute } from '@react-navigation/native';
import { RootStackParamList } from 'types';
import { useTranslation } from 'react-i18next';
import styled from '@emotion/native';
import {
  H5,
  H6,
  Icon,
} from 'cube-ui-components';
import { colors, sizes } from 'cube-ui-components/dist/cjs/theme/base';
import { reportBackground } from 'features/ecoach/assets';
import {
  ImageBackground,
  Platform,
  ScrollView,
  TouchableOpacity,
  View,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { StatusBar } from 'expo-status-bar';
import { ConversationDataV2 } from 'features/ecoach/api/conversationApi';
import ImprovementCard from 'features/ecoach/components/v2/ImprovementCard';
import StrengthCard from 'features/ecoach/components/v2/StrengthCard';

// Styled Components
const PageImageBackground = styled(ImageBackground)(() => ({
  flex: 1,
  width: '100%',
  height: '100%',
}));

const BackBtn = styled(TouchableOpacity)(() => ({
  paddingTop: Platform.OS === 'ios' ? 0 : sizes[5],
  paddingHorizontal: sizes[4],
}));

const HeaderContainer = styled(View)(() => ({
  backgroundColor: colors.fwdDarkGreen[100],
  paddingHorizontal: sizes[4],
  paddingVertical: sizes[3],
  borderRadius: 24,
  marginHorizontal: sizes[4],
  marginTop: sizes[4],
}));

const TabContainer = styled(View)(() => ({
  flexDirection: 'row',
  backgroundColor: colors.white,
  borderRadius: 12,
  marginHorizontal: sizes[4],
  marginTop: sizes[4],
  padding: sizes[1],
}));

const Tab = styled(TouchableOpacity)<{ active: boolean }>(({ active }) => ({
  flex: 1,
  paddingVertical: sizes[3],
  paddingHorizontal: sizes[4],
  borderRadius: 8,
  backgroundColor: active ? colors.fwdOrange[100] : 'transparent',
  alignItems: 'center',
}));

const TabText = styled(H6)<{ active: boolean }>(({ active }) => ({
  color: active ? colors.white : colors.fwdDarkGreen[70],
  fontWeight: active ? 'bold' : '500',
}));

const ContentContainer = styled(View)(() => ({
  paddingHorizontal: sizes[4],
  paddingTop: sizes[4],
}));

const SectionTitle = styled(H5)(() => ({
  color: colors.fwdDarkGreen[100],
  fontWeight: 'bold',
  marginBottom: sizes[4],
}));

interface DetailedFeedbackV1Props {
  conversationData: ConversationDataV2;
}

const DetailedFeedbackV1: React.FC<DetailedFeedbackV1Props> = ({ conversationData }) => {
  const { t } = useTranslation('ecoach');
  const navigation = useNavigation<NativeStackNavigationProp<RootStackParamList>>();
  const [activeTab, setActiveTab] = useState<'improvements' | 'strengths'>('improvements');
  
  const { skill_set_details } = conversationData.report;

  // Collect all improvement and strength cards
  const allImprovementCards = skill_set_details.flatMap(skill => skill.improvement.cards);
  const allStrengthCards = skill_set_details.flatMap(skill => skill.strengths.cards);

  const goBack = () => {
    navigation.goBack();
  };

  return (
    <View style={{ flex: 1 }}>
      <StatusBar hidden />
      <PageImageBackground source={reportBackground} resizeMode="cover">
        <SafeAreaView style={{ flex: 1 }}>
          <BackBtn onPress={goBack}>
            <Icon.ArrowLeft fill={colors.fwdDarkGreen[100]} />
          </BackBtn>

          {/* Header Section */}
          <HeaderContainer>
            <H6 color={colors.white} style={{ textAlign: 'center' }}>
              {t('detailedFeedback')}
            </H6>
            <H5 color={colors.white} fontWeight="bold" style={{ textAlign: 'center' }}>
              {t('yourReport')}
            </H5>
          </HeaderContainer>

          {/* Tab Navigation */}
          <TabContainer>
            <Tab 
              active={activeTab === 'improvements'} 
              onPress={() => setActiveTab('improvements')}
            >
              <TabText active={activeTab === 'improvements'}>
                {t('improvements')} ({allImprovementCards.length})
              </TabText>
            </Tab>
            <Tab 
              active={activeTab === 'strengths'} 
              onPress={() => setActiveTab('strengths')}
            >
              <TabText active={activeTab === 'strengths'}>
                {t('strengths')} ({allStrengthCards.length})
              </TabText>
            </Tab>
          </TabContainer>

          <ScrollView showsVerticalScrollIndicator={false}>
            <ContentContainer>
              {activeTab === 'improvements' ? (
                <>
                  <SectionTitle>{t('areasForImprovement')}</SectionTitle>
                  {allImprovementCards.map((card, index) => (
                    <ImprovementCard key={index} card={card} index={index} />
                  ))}
                </>
              ) : (
                <>
                  <SectionTitle>{t('yourStrengths')}</SectionTitle>
                  {allStrengthCards.map((card, index) => (
                    <StrengthCard key={index} card={card} index={index} />
                  ))}
                </>
              )}
            </ContentContainer>
          </ScrollView>
        </SafeAreaView>
      </PageImageBackground>
    </View>
  );
};

export default DetailedFeedbackV1;
