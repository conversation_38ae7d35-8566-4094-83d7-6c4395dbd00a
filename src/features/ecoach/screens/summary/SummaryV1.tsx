import React, { useState } from 'react';
import { RouteProp, useNavigation, useRoute } from '@react-navigation/native';
import { RootStackParamList } from 'types';
import { useTranslation } from 'react-i18next';
import styled from '@emotion/native';
import {
  Button,
  H1,
  H2,
  H3,
  H4,
  H5,
  H6,
  H7,
  H8,
  Icon,
  Label,
  Body,
  SmallLabel,
} from 'cube-ui-components';
import { colors, sizes } from 'cube-ui-components/dist/cjs/theme/base';
import { reportBackground } from 'features/ecoach/assets';
import {
  Image,
  ImageBackground,
  Platform,
  ScrollView,
  TouchableOpacity,
  View,
} from 'react-native';
import { format, parseISO } from 'date-fns';
import { EcoachParamList } from 'types/navigation';
import { SafeAreaView } from 'react-native-safe-area-context';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { StatusBar } from 'expo-status-bar';
import { ConversationDataV2, FeedbackCard, PraiseCard } from 'features/ecoach/api/conversationApi';

// Styled Components
const PageImageBackground = styled(ImageBackground)(() => ({
  flex: 1,
  width: '100%',
  height: '100%',
}));

const BackBtn = styled(TouchableOpacity)(() => ({
  paddingTop: Platform.OS === 'ios' ? 0 : sizes[5],
  paddingHorizontal: sizes[4],
}));

const HeaderContainer = styled(View)(() => ({
  backgroundColor: colors.fwdDarkGreen[100],
  paddingHorizontal: sizes[4],
  paddingVertical: sizes[3],
  borderRadius: 24,
  marginHorizontal: sizes[4],
  marginTop: sizes[4],
}));

const ScoreContainer = styled(View)(() => ({
  alignItems: 'center',
  paddingVertical: sizes[6],
}));

const ScoreText = styled(H1)(() => ({
  color: colors.fwdOrange[100],
  fontSize: 48,
  fontWeight: 'bold',
}));

const LastScoreContainer = styled(View)(() => ({
  flexDirection: 'row',
  alignItems: 'center',
  justifyContent: 'center',
  marginTop: sizes[2],
}));

const LastScoreText = styled(H6)(() => ({
  color: colors.fwdDarkGreen[50],
  marginRight: sizes[2],
}));

const LastScoreValue = styled(H6)(() => ({
  color: colors.fwdDarkGreen[100],
  fontWeight: 'bold',
}));

const BadgeContainer = styled(View)(() => ({
  flexDirection: 'row',
  justifyContent: 'center',
  gap: sizes[2],
  marginTop: sizes[4],
}));

const Badge = styled(View)<{ backgroundColor: string }>(({ backgroundColor }) => ({
  backgroundColor,
  paddingHorizontal: sizes[3],
  paddingVertical: sizes[1],
  borderRadius: 4,
}));

const BadgeText = styled(SmallLabel)(() => ({
  color: colors.white,
  fontWeight: 'bold',
}));

const SummarySection = styled(View)(() => ({
  backgroundColor: colors.white,
  borderRadius: 24,
  marginHorizontal: sizes[4],
  marginTop: sizes[6],
  padding: sizes[4],
}));

const SectionTitle = styled(H5)(() => ({
  color: colors.fwdDarkGreen[100],
  fontWeight: 'bold',
  marginBottom: sizes[3],
}));

const SummaryText = styled(Body)(() => ({
  color: colors.fwdDarkGreen[100],
  lineHeight: 24,
}));

const SkillChartContainer = styled(View)(() => ({
  backgroundColor: colors.white,
  marginHorizontal: sizes[4],
  marginTop: sizes[4],
  padding: sizes[4],
  borderRadius: 12,
}));

const SkillBarContainer = styled(View)(() => ({
  marginBottom: sizes[4],
}));

const SkillName = styled(H7)(() => ({
  color: colors.fwdDarkGreen[100],
  fontWeight: '500',
  marginBottom: sizes[2],
  textAlign: 'center',
}));

const SkillBarBackground = styled(View)(() => ({
  height: 83,
  backgroundColor: colors.fwdDarkGreen[10],
  borderRadius: 8,
  borderWidth: 1,
  borderColor: colors.white,
  overflow: 'hidden',
}));

const SkillBarFill = styled(View)<{ height: number; backgroundColor: string }>(
  ({ height, backgroundColor }) => ({
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    height: `${height}%`,
    backgroundColor,
    borderRadius: height === 100 ? 8 : '0px 0px 8px 8px',
  })
);

const SkillScore = styled(H7)(() => ({
  color: colors.fwdDarkGreen[100],
  fontWeight: 'bold',
  textAlign: 'center',
  marginTop: sizes[2],
}));

const SkillChartRow = styled(View)(() => ({
  flexDirection: 'row',
  justifyContent: 'center',
  alignItems: 'flex-end',
  gap: 4,
}));

const ImprovementSection = styled(View)(() => ({
  backgroundColor: colors.white,
  marginHorizontal: sizes[4],
  marginTop: sizes[4],
  padding: sizes[4],
  borderRadius: 12,
}));

const ImprovementHeader = styled(View)(() => ({
  flexDirection: 'row',
  alignItems: 'center',
  marginBottom: sizes[3],
}));

const ImprovementIcon = styled(View)(() => ({
  width: 24,
  height: 20,
  marginRight: sizes[2],
  // Add improvement icon styling
}));

const ImprovementTitle = styled(H6)(() => ({
  color: colors.fwdDarkGreen[100],
  fontWeight: '500',
}));

const ImprovementSummary = styled(Body)(() => ({
  color: colors.fwdDarkGreen[100],
  marginBottom: sizes[4],
}));

const SeeMoreButton = styled(TouchableOpacity)(() => ({
  flexDirection: 'row',
  justifyContent: 'space-between',
  alignItems: 'center',
  paddingVertical: sizes[3],
}));

const SeeMoreText = styled(H7)(() => ({
  color: colors.fwdDarkGreen[50],
  fontWeight: '500',
}));

const ButtonContainer = styled(View)(() => ({
  paddingHorizontal: sizes[4],
  paddingVertical: sizes[6],
}));

interface SummaryV1Props {
  conversationData: ConversationDataV2;
  session?: any;
  reTake?: () => void;
}

const SummaryV1: React.FC<SummaryV1Props> = ({ conversationData, session, reTake }) => {
  const { t } = useTranslation('ecoach');
  const navigation = useNavigation<NativeStackNavigationProp<RootStackParamList>>();
  
  const { conversation_id, datetime, difficulty, duration, report } = conversationData;
  const { overall_score, last_score, overall_summary, skill_set_details } = report;

  const goBackBtn = () => {
    if (session) {
      navigation.goBack();
    } else {
      navigation.navigate('EcoachHome');
    }
  };

  const tryAgain = () => {
    navigation.goBack();
    reTake && reTake();
  };

  const goHistoryScreen = () => {
    navigation.push('SessionHistory', {});
  };

  const convertSecondsToMins = (duration: number) => {
    const minutes = Math.floor(duration / 60);
    const seconds = (duration % 60).toFixed(2);
    return `${minutes} ${t('min')} ${seconds} ${t('sec')}`;
  };

  const getSkillBarColor = (score: string) => {
    const numScore = parseInt(score);
    if (numScore >= 80) return colors.fwdLightGreen[100];
    if (numScore >= 60) return colors.fwdOrange[100];
    if (numScore >= 30) return colors.fwdRed[100];
    return colors.fwdDarkGreen[20];
  };

  const getBadgeInfo = (score: string) => {
    const numScore = parseInt(score);
    if (numScore >= 80) return { text: 'Perfect', color: colors.fwdLightGreen[100] };
    if (numScore >= 60) return { text: 'Pass', color: colors.fwdOrange[100] };
    return { text: 'Needs Work', color: colors.fwdRed[100] };
  };

  const overallScoreNum = parseInt(overall_score);
  const lastScoreNum = parseInt(last_score);
  const badgeInfo = getBadgeInfo(overall_score);

  return (
    <View style={{ flex: 1 }}>
      <StatusBar hidden />
      <PageImageBackground source={reportBackground} resizeMode="cover">
        <SafeAreaView style={{ flex: 1 }}>
          <BackBtn onPress={goBackBtn}>
            {session ? (
              <Icon.ArrowLeft fill={colors.fwdDarkGreen[100]} />
            ) : (
              <Icon.Close fill={colors.fwdDarkGreen[100]} />
            )}
          </BackBtn>

          <ScrollView showsVerticalScrollIndicator={false}>
            {/* Header Section */}
            <HeaderContainer>
              <H6 color={colors.white} style={{ textAlign: 'center' }}>
                {t('detailedFeedback')}
              </H6>
              <H5 color={colors.white} fontWeight="bold" style={{ textAlign: 'center' }}>
                {t('yourReport')}
              </H5>
            </HeaderContainer>

            {/* Score Section */}
            <ScoreContainer>
              <SmallLabel color={colors.white}>{t('yourScore')}</SmallLabel>
              <ScoreText>{overallScoreNum}</ScoreText>
              
              <LastScoreContainer>
                <LastScoreText>{t('lastScore')}</LastScoreText>
                <LastScoreValue>{lastScoreNum}</LastScoreValue>
              </LastScoreContainer>

              <BadgeContainer>
                <Badge backgroundColor={badgeInfo.color}>
                  <BadgeText>{badgeInfo.text}</BadgeText>
                </Badge>
              </BadgeContainer>

              <H6 color={colors.white} style={{ textAlign: 'center', marginTop: sizes[4] }}>
                Ultimate roleplay
              </H6>
            </ScoreContainer>

            {/* Summary Section */}
            <SummarySection>
              <SectionTitle>{t('summary')}</SectionTitle>
              <SummaryText>{overall_summary}</SummaryText>
            </SummarySection>

            {/* Skill Chart */}
            <SkillChartContainer>
              <SkillChartRow>
                {skill_set_details.slice(0, 4).map((skill, index) => (
                  <SkillBarContainer key={index}>
                    <SkillBarBackground>
                      <SkillBarFill
                        height={parseInt(skill.score)}
                        backgroundColor={getSkillBarColor(skill.score)}
                      />
                    </SkillBarBackground>
                    <SkillScore>{skill.score}</SkillScore>
                    <SkillName>{skill.name.replace(/\s+/g, '\n')}</SkillName>
                  </SkillBarContainer>
                ))}
              </SkillChartRow>
            </SkillChartContainer>

            {/* Improvement Section */}
            <ImprovementSection>
              <ImprovementHeader>
                <ImprovementIcon />
                <ImprovementTitle>{t('improvements')}</ImprovementTitle>
              </ImprovementHeader>
              
              <ImprovementSummary>
                {skill_set_details[0]?.improvement?.summary || ''}
              </ImprovementSummary>

              <SeeMoreButton>
                <SeeMoreText>
                  {t('see')} {skill_set_details.reduce((total, skill) => 
                    total + skill.improvement.cards.length, 0
                  )} {t('improvements')}
                </SeeMoreText>
                <Icon.ChevronUp fill={colors.fwdOrange[100]} />
              </SeeMoreButton>
            </ImprovementSection>

            {/* Action Buttons */}
            {!session && (
              <ButtonContainer>
                <Button
                  variant="primary"
                  text={t('tryAgain')}
                  onPress={tryAgain}
                  style={{ marginBottom: sizes[3] }}
                />
                <Button
                  variant="secondary"
                  text={t('viewHistory')}
                  onPress={goHistoryScreen}
                />
              </ButtonContainer>
            )}
          </ScrollView>
        </SafeAreaView>
      </PageImageBackground>
    </View>
  );
};

export default SummaryV1;
