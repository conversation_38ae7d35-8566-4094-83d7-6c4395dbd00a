import React from 'react';
import { View } from 'react-native';
import SummaryV1 from './SummaryV1';
import { mockConversationDataV2 } from './mockDataV2';

/**
 * Test component to verify SummaryV1 works with mock data
 * This can be used for development and testing purposes
 */
const SummaryV1Test: React.FC = () => {
  const mockReTake = () => {
    console.log('Mock reTake function called');
  };

  return (
    <View style={{ flex: 1 }}>
      <SummaryV1 
        conversationData={mockConversationDataV2}
        session={undefined}
        reTake={mockReTake}
      />
    </View>
  );
};

export default SummaryV1Test;
